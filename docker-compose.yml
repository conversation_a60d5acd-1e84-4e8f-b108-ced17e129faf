version: '3.8'

services:
  api:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - .:/app
      - /tmp/media_processing:/tmp/media_processing
    environment:
      - DEBUG=true
      - STORAGE_TYPE=local
      - REDIS_HOST=redis
    depends_on:
      - redis
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  celery_worker:
    build: .
    command: celery -A app.utils.job_queue.celery_app worker --loglevel=info
    volumes:
      - .:/app
      - /tmp/media_processing:/tmp/media_processing
    environment:
      - DEBUG=true
      - STORAGE_TYPE=local
      - REDIS_HOST=redis
    depends_on:
      - redis
    restart: unless-stopped

volumes:
  redis_data:
