import os
from pathlib import Path
from typing import Dict, Any, Optional

from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    """Application settings."""
    
    # Base
    DEBUG: bool = False
    APP_NAME: str = "Media Processing API"
    VERSION: str = "2.0.0"
    API_PREFIX: str = "/api/v2"
    
    # Security
    JWT_SECRET_KEY: str = os.getenv("JWT_SECRET_KEY", "your-secret-key")
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Storage
    STORAGE_TYPE: str = os.getenv("STORAGE_TYPE", "local")  # local, s3, or gcp
    TEMP_DIR: Path = Path("/tmp/media_processing")
    
    # AWS S3
    AWS_ACCESS_KEY_ID: Optional[str] = os.getenv("AWS_ACCESS_KEY_ID")
    AWS_SECRET_ACCESS_KEY: Optional[str] = os.getenv("AWS_SECRET_ACCESS_KEY")
    AWS_REGION: Optional[str] = os.getenv("AWS_REGION", "us-east-1")
    S3_BUCKET: Optional[str] = os.getenv("S3_BUCKET")
    
    # Google Cloud Storage
    GCP_PROJECT_ID: Optional[str] = os.getenv("GCP_PROJECT_ID")
    GCP_CREDENTIALS_PATH: Optional[str] = os.getenv("GCP_CREDENTIALS_PATH")
    GCS_BUCKET: Optional[str] = os.getenv("GCS_BUCKET")
    
    # Redis
    REDIS_HOST: str = os.getenv("REDIS_HOST", "localhost")
    REDIS_PORT: int = int(os.getenv("REDIS_PORT", "6379"))
    REDIS_DB: int = int(os.getenv("REDIS_DB", "0"))
    
    # Media Processing
    MAX_UPLOAD_SIZE: int = 1024 * 1024 * 100  # 100MB
    SUPPORTED_VIDEO_FORMATS: str = "mp4,mov,avi,mkv"
    SUPPORTED_AUDIO_FORMATS: str = "mp3,wav,aac,flac"
    SUPPORTED_IMAGE_FORMATS: str = "jpg,png,gif,webp"

    # Whisper Configuration
    WHISPER_MODEL: str = os.getenv("WHISPER_MODEL", "base")  # base, small, medium, large
    
    # Premium Features
    PREMIUM_VIDEO_EFFECTS: str = "blur,grayscale,sepia"
    PREMIUM_AUDIO_EFFECTS: str = "echo,reverb"
    
    def get_redis_url(self) -> str:
        """Get Redis URL."""
        return f"redis://{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
    
    def get_storage_config(self) -> Dict[str, Any]:
        """Get storage configuration."""
        if self.STORAGE_TYPE == "s3":
            return {
                "aws_access_key_id": self.AWS_ACCESS_KEY_ID,
                "aws_secret_access_key": self.AWS_SECRET_ACCESS_KEY,
                "region_name": self.AWS_REGION,
                "bucket_name": self.S3_BUCKET
            }
        elif self.STORAGE_TYPE == "gcp":
            return {
                "project_id": self.GCP_PROJECT_ID,
                "credentials_path": self.GCP_CREDENTIALS_PATH,
                "bucket_name": self.GCS_BUCKET
            }
        else:
            return {}
    
    class Config:
        case_sensitive = True
        env_file = ".env"
        extra = "allow"  # Allow extra environment variables

    @property
    def video_formats_list(self) -> list:
        """Get video formats as a list."""
        return self.SUPPORTED_VIDEO_FORMATS.split(",")

    @property
    def audio_formats_list(self) -> list:
        """Get audio formats as a list."""
        return self.SUPPORTED_AUDIO_FORMATS.split(",")

    @property
    def image_formats_list(self) -> list:
        """Get image formats as a list."""
        return self.SUPPORTED_IMAGE_FORMATS.split(",")

    @property
    def premium_video_effects_list(self) -> list:
        """Get premium video effects as a list."""
        return self.PREMIUM_VIDEO_EFFECTS.split(",")

    @property
    def premium_audio_effects_list(self) -> list:
        """Get premium audio effects as a list."""
        return self.PREMIUM_AUDIO_EFFECTS.split(",")

# Create settings instance
settings = Settings()
