import logging
from typing import Dict, Any

import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError

from app.config import settings
from app.models import ErrorResponse
from app.utils.storage import storage_provider
from app.routes.v2 import video, audio, image, media

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.VERSION,
    docs_url=f"{settings.API_PREFIX}/docs",
    redoc_url=f"{settings.API_PREFIX}/redoc",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """Initialize services on startup."""
    try:
        # Initialize storage provider
        await storage_provider.initialize()
        logger.info("Storage provider initialized successfully")
        
    except Exception as e:
        logger.error(f"Error during startup: {str(e)}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown."""
    try:
        # Cleanup temporary files
        await storage_provider.cleanup()
        logger.info("Cleanup completed successfully")
        
    except Exception as e:
        logger.error(f"Error during shutdown: {str(e)}")

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """Handle validation errors."""
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=ErrorResponse(
            detail="Invalid request parameters",
            code="VALIDATION_ERROR",
            params={"errors": exc.errors()}
        ).dict()
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle general exceptions."""
    logger.error(f"Unhandled exception: {str(exc)}")
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=ErrorResponse(
            detail="Internal server error",
            code="INTERNAL_ERROR"
        ).dict()
    )

@app.get(f"{settings.API_PREFIX}/health")
async def health_check() -> Dict[str, Any]:
    """Health check endpoint."""
    storage_status = storage_provider.get_status()
    
    return {
        "status": "healthy" if storage_status["status"] == "healthy" else "unhealthy",
        "version": settings.VERSION,
        "storage": storage_status
    }

# Include routers
app.include_router(
    video.router,
    prefix=f"{settings.API_PREFIX}/video",
    tags=["video"]
)

app.include_router(
    audio.router,
    prefix=f"{settings.API_PREFIX}/audio",
    tags=["audio"]
)

app.include_router(
    image.router,
    prefix=f"{settings.API_PREFIX}/image",
    tags=["image"]
)

app.include_router(
    media.router,
    prefix=f"{settings.API_PREFIX}/media",
    tags=["media"]
)

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG
    )
