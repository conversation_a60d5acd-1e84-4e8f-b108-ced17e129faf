version: '3.8'

services:
  api:
    build: .
    expose:
      - "8000"
    volumes:
      - /tmp/media_processing:/tmp/media_processing
    environment:
      - DEBUG=false
      - STORAGE_TYPE=local
      - REDIS_HOST=redis
      - API_VERSION=v2
      - PYTHONPATH=/app
      - WHISPER_MODEL=base
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v2/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  celery_worker:
    build: .
    command: celery -A app.utils.job_queue.celery_app worker --loglevel=info
    volumes:
      - /tmp/media_processing:/tmp/media_processing
    environment:
      - DEBUG=false
      - STORAGE_TYPE=local
      - REDIS_HOST=redis
      - API_VERSION=v2
      - PYTHONPATH=/app
      - WHISPER_MODEL=base
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - redis
    restart: unless-stopped

volumes:
  redis_data:
